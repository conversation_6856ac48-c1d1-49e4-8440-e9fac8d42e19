import { <PERSON><PERSON><PERSON>, Player, Vector3, GameMode, EntityDamageCause, system, world } from "@minecraft/server";
import { getDistance } from "../utilities/vector3";
import { fixedLenRaycast } from "../utilities/raycasts";

/**
 * @fileoverview Werewolf Entity Handler for DitSH Add-On
 *
 * This module handles the werewolf entity behavior including:
 * - Gradual health drain for players looking at the werewolf (starts after 3 seconds, increases every 2 seconds)
 * - Teleportation to players not looking at the werewolf (direct teleportation to player location)
 * - Player detection and line-of-sight checking using fixed-length raycast
 * - Automatic cleanup of player tracking data to prevent memory leaks
 *
 * The werewolf uses a dual behavior system:
 * 1. Health Drain: Players looking at the werewolf take progressive damage over time
 * 2. Teleportation: The werewolf teleports to players who are NOT looking at it
 *
 * <AUTHOR>
 * @version 2.0.0
 */

/** Base damage dealt to players looking at werewolf (starts after 3 seconds) */
const BASE_WEREWOLF_DAMAGE: number = 2;

/** Detection radius for players (128 blocks) */
const DETECTION_RADIUS: number = 128;

/** Time in ticks before damage starts (3 seconds = 60 ticks) */
const DAMAGE_START_TICKS: number = 60;

/** Time in ticks between damage increments (2 seconds = 40 ticks) */
const DAMAGE_INCREMENT_TICKS: number = 40;

/** Map to track when each player started looking at the werewolf */
const playerLookAtTracker = new Map<string, number>();

/** Raycast parameters for line-of-sight detection */
const RAYCAST_RADIUS: number = 5;
const RAYCAST_STEP: number = 2;

/**
 * Checks if a player is looking at the werewolf using fixed length raycast.
 * Uses 2-block step size and 5-block radius detection per raycast point.
 *
 * @param player - The player to check
 * @param werewolfLocation - The werewolf's current location
 * @returns True if the player is looking at the werewolf
 */
function isPlayerLookingAtWerewolf(player: Player, werewolfLocation: Vector3): boolean {
  try {
    const playerLocation: Vector3 = {
      x: player.location.x,
      y: player.location.y + 1.6, // Eye level
      z: player.location.z
    };

    const viewDirection: Vector3 = player.getViewDirection();
    const maxDistance: number = getDistance(playerLocation, werewolfLocation);
    const raycastStep: number = RAYCAST_STEP;
    const detectionRadius: number = RAYCAST_RADIUS;

    // Perform fixed length raycast from player's head location
    const raycastPoints: Vector3[] = fixedLenRaycast(playerLocation, viewDirection, maxDistance, raycastStep);

    // Check each raycast point for proximity to werewolf
    for (const rayPoint of raycastPoints) {
      const distanceToWerewolf: number = getDistance(rayPoint, werewolfLocation);

      if (distanceToWerewolf <= detectionRadius) {
        // Check if the raycast point is within the vertical detection area (3 blocks high)
        if (rayPoint.y >= werewolfLocation.y && rayPoint.y <= werewolfLocation.y + 3.0) {
          return true;
        }
      }
    }

    return false;
  } catch (error) {
    return false;
  }
}

/**
 * Handles gradual health drain for players looking at the werewolf.
 * Tracks how long each player has been looking and applies progressive damage.
 * Damage starts after 3 seconds and increases every 2 seconds thereafter.
 *
 * @param werewolf - The werewolf entity
 */
export function werewolfHealthDrain(werewolf: Entity): void {
  try {
    const werewolfLocation: Vector3 = werewolf.location;
    const currentTick: number = system.currentTick;

    // Get all valid players within detection radius
    const players: Player[] = werewolf.dimension.getPlayers({
      location: werewolfLocation,
      maxDistance: DETECTION_RADIUS,
      excludeGameModes: [GameMode.Creative, GameMode.Spectator]
    });

    // Track which players are currently looking
    const playersCurrentlyLooking = new Set<string>();

    // Check each player and handle damage/tracking
    for (const player of players) {
      const playerId: string = player.id;

      if (isPlayerLookingAtWerewolf(player, werewolfLocation)) {
        playersCurrentlyLooking.add(playerId);

        // If player wasn't being tracked, start tracking them
        if (!playerLookAtTracker.has(playerId)) {
          playerLookAtTracker.set(playerId, currentTick);
        }

        // Calculate how long they've been looking
        const lookStartTick: number = playerLookAtTracker.get(playerId)!;
        const lookDuration: number = currentTick - lookStartTick;

        // Apply damage if they've been looking for 3+ seconds
        if (lookDuration >= DAMAGE_START_TICKS) {
          // Calculate damage based on how long they've been looking
          const damageIntervals: number = Math.floor((lookDuration - DAMAGE_START_TICKS) / DAMAGE_INCREMENT_TICKS);
          const damage: number = BASE_WEREWOLF_DAMAGE + damageIntervals;

          // Apply damage
          player.applyDamage(damage, {
            cause: EntityDamageCause.entityAttack,
            damagingEntity: werewolf
          });

          // Play damage sound effect
          werewolf.dimension.playSound("mob.ditsh.werewolf.drain", player.location);
        }
      }
    }

    // Clean up tracking for players who stopped looking
    for (const [playerId] of playerLookAtTracker) {
      if (!playersCurrentlyLooking.has(playerId)) {
        playerLookAtTracker.delete(playerId);
      }
    }
  } catch (error) {
    console.warn(`Failed to handle werewolf health drain: ${error}`);
  }
}

/**
 * Finds players who are not looking at the werewolf and are within range.
 *
 * @param werewolf - The werewolf entity
 * @returns Array of players not looking at the werewolf
 */
function getPlayersNotLookingAtWerewolf(werewolf: Entity): Player[] {
  const werewolfLocation: Vector3 = werewolf.location;
  const playersNotLooking: Player[] = [];

  // Get all valid players within detection radius
  const players: Player[] = werewolf.dimension.getPlayers({
    location: werewolfLocation,
    maxDistance: DETECTION_RADIUS,
    excludeGameModes: [GameMode.Creative, GameMode.Spectator]
  });

  for (const player of players) {
    if (!isPlayerLookingAtWerewolf(player, werewolfLocation)) {
      playersNotLooking.push(player);
    }
  }

  return playersNotLooking;
}

/**
 * Teleports the werewolf directly to a player who is not looking at it.
 * This function is called when the werewolf should teleport to nearby players.
 *
 * @param werewolf - The werewolf entity
 */
export function werewolfTeleportToPlayer(werewolf: Entity): void {
  try {
    const playersNotLooking: Player[] = getPlayersNotLookingAtWerewolf(werewolf);

    if (playersNotLooking.length === 0) {
      return;
    }

    // Select a random player not looking at the werewolf
    const targetPlayer: Player = playersNotLooking[Math.floor(Math.random() * playersNotLooking.length)]!;

    // Teleport werewolf directly to the player's location
    werewolf.teleport(targetPlayer.location);

    // Play teleport sound effect
    werewolf.dimension.playSound("mob.ditsh.werewolf.teleport", targetPlayer.location);

    // Reset any tracking for this player since werewolf moved
    playerLookAtTracker.delete(targetPlayer.id);
  } catch (error) {
    console.warn(`Failed to teleport werewolf to player: ${error}`);
  }
}

/**
 * Cleans up tracking data for disconnected players to prevent memory leaks.
 * This function should be called periodically to maintain clean state.
 */
export function cleanupWerewolfPlayerTracking(): void {
  try {
    // Get all currently connected players
    const connectedPlayerIds = new Set<string>();

    // Check all dimensions for connected players
    for (const dimension of [world.getDimension("overworld"), world.getDimension("nether"), world.getDimension("the_end")]) {
      try {
        const players: Player[] = dimension.getPlayers();
        for (const player of players) {
          connectedPlayerIds.add(player.id);
        }
      } catch (error) {
        // Dimension might not be loaded, continue with others
        continue;
      }
    }

    // Remove tracking data for disconnected players
    for (const [playerId] of playerLookAtTracker) {
      if (!connectedPlayerIds.has(playerId)) {
        playerLookAtTracker.delete(playerId);
      }
    }
  } catch (error) {
    console.warn(`Failed to cleanup werewolf player tracking: ${error}`);
  }
}
