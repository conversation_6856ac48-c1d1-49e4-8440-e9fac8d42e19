import { <PERSON><PERSON><PERSON>, Player, Vector3, GameMode, EntityDamageCause, system } from "@minecraft/server";
import { getDistance } from "../utilities/vector3";
import { fixedLenRaycast } from "../utilities/raycasts";

/**
 * @fileoverview Werewolf Entity Handler for DitSH Add-On
 *
 * This module handles the werewolf entity behavior including:
 * - Gradual health draining when players look at it
 * - Teleportation to nearby players when no one is looking
 * - Player detection and line-of-sight checking
 *
 * <AUTHOR>
 * @version 1.1.0
 */

/** Damage dealt per second to players looking at werewolf (1 heart = 2 damage) */
const WEREWOLF_DRAIN_DAMAGE: number = 2;

/** Detection radius for players (128 blocks) */
const DETECTION_RADIUS: number = 128;

/** Interval for health draining check (in ticks, 20 ticks = 1 second) */
const HEALTH_DRAIN_INTERVAL: number = 20;

/** Teleportation range when no one is looking (blocks) */
const TELEPORT_RANGE: number = 10;

/** Raycast parameters for line-of-sight detection */
const RAYCAST_RADIUS: number = 5;
const RAYCAST_STEP: number = 2;

/**
 * Checks if a player is looking at the werewolf using fixed length raycast.
 * Uses 2-block step size and 5-block radius detection per raycast point.
 *
 * @param player - The player to check
 * @param werewolfLocation - The werewolf's current location
 * @returns True if the player is looking at the werewolf
 */
function isPlayerLookingAtWerewolf(player: Player, werewolfLocation: Vector3): boolean {
  try {
    const playerLocation: Vector3 = {
      x: player.location.x,
      y: player.location.y + 1.6, // Eye level
      z: player.location.z
    };

    const viewDirection: Vector3 = player.getViewDirection();
    const maxDistance: number = getDistance(playerLocation, werewolfLocation);
    const raycastStep: number = RAYCAST_STEP;
    const detectionRadius: number = RAYCAST_RADIUS;

    // Perform fixed length raycast from player's head location
    const raycastPoints: Vector3[] = fixedLenRaycast(playerLocation, viewDirection, maxDistance, raycastStep);

    // Check each raycast point for proximity to werewolf
    for (const rayPoint of raycastPoints) {
      const distanceToWerewolf: number = getDistance(rayPoint, werewolfLocation);

      if (distanceToWerewolf <= detectionRadius) {
        // Check if the raycast point is within the vertical detection area (3 blocks high)
        if (rayPoint.y >= werewolfLocation.y && rayPoint.y <= werewolfLocation.y + 3.0) {
          return true;
        }
      }
    }

    return false;
  } catch (error) {
    return false;
  }
}

/** Map to track active werewolf entities for health draining */
const activeWerewolfEntities: Map<string, number> = new Map();

/**
 * Starts the gradual health draining effect for players looking at the werewolf.
 * This function is triggered by the looked_at component and sets up a periodic check.
 *
 * @param werewolf - The werewolf entity
 */
export function werewolfStartHealthDrain(werewolf: Entity): void {
  try {
    const werewolfId: string = werewolf.id;

    // Check if this werewolf is already draining health
    if (activeWerewolfEntities.has(werewolfId)) {
      return;
    }

    // Start the health draining interval
    const intervalId: number = system.runInterval(() => {
      try {
        // Check if the werewolf still exists
        if (!werewolf.isValid) {
          system.clearRun(intervalId);
          activeWerewolfEntities.delete(werewolfId);
          return;
        }

        const werewolfLocation: Vector3 = werewolf.location;

        // Get all valid players within detection radius
        const players: Player[] = werewolf.dimension.getPlayers({
          location: werewolfLocation,
          maxDistance: DETECTION_RADIUS,
          excludeGameModes: [GameMode.Creative, GameMode.Spectator]
        });

        // Apply damage to players looking at the werewolf
        for (const player of players) {
          if (isPlayerLookingAtWerewolf(player, werewolfLocation)) {
            // Apply gradual damage (1 heart per second)
            player.applyDamage(WEREWOLF_DRAIN_DAMAGE, {
              cause: EntityDamageCause.entityAttack,
              damagingEntity: werewolf
            });

            // Play drain sound effect (using a generic hurt sound)
            werewolf.dimension.playSound("damage.hit", player.location, {
              volume: 0.3,
              pitch: 0.8
            });
          }
        }
      } catch (error) {
        console.warn(`Failed to drain health for werewolf ${werewolfId}: ${error}`);
        system.clearRun(intervalId);
        activeWerewolfEntities.delete(werewolfId);
      }
    }, HEALTH_DRAIN_INTERVAL);

    // Store the interval ID for cleanup
    activeWerewolfEntities.set(werewolfId, intervalId);
  } catch (error) {
    console.warn(`Failed to start werewolf health drain: ${error}`);
  }
}

/**
 * Stops the health draining effect for a werewolf entity.
 * This function is called when the werewolf dies or is removed.
 *
 * @param werewolf - The werewolf entity
 */
export function werewolfStopHealthDrain(werewolf: Entity): void {
  try {
    const werewolfId: string = werewolf.id;
    const intervalId: number | undefined = activeWerewolfEntities.get(werewolfId);

    if (intervalId !== undefined) {
      system.clearRun(intervalId);
      activeWerewolfEntities.delete(werewolfId);
    }
  } catch (error) {
    console.warn(`Failed to stop werewolf health drain: ${error}`);
  }
}

/**
 * Handles werewolf teleportation to nearby players when no one is looking at it.
 * This function is triggered by an environment sensor or timer component.
 *
 * @param werewolf - The werewolf entity
 */
export function werewolfTeleportToPlayer(werewolf: Entity): void {
  try {
    const werewolfLocation: Vector3 = werewolf.location;

    // Get all valid players within detection radius
    const players: Player[] = werewolf.dimension.getPlayers({
      location: werewolfLocation,
      maxDistance: DETECTION_RADIUS,
      excludeGameModes: [GameMode.Creative, GameMode.Spectator]
    });

    if (players.length === 0) {
      return;
    }

    // Check if any player is looking at the werewolf
    let anyPlayerLooking: boolean = false;
    for (const player of players) {
      if (isPlayerLookingAtWerewolf(player, werewolfLocation)) {
        anyPlayerLooking = true;
        break;
      }
    }

    // Only teleport if no one is looking
    if (!anyPlayerLooking) {
      // Find the nearest player
      let nearestPlayer: Player | null = null;
      let nearestDistance: number = Infinity;

      for (const player of players) {
        const distance: number = getDistance(werewolfLocation, player.location);
        if (distance < nearestDistance) {
          nearestDistance = distance;
          nearestPlayer = player;
        }
      }

      if (nearestPlayer) {
        // Calculate a random teleport location near the player
        const randomAngle: number = Math.random() * 2 * Math.PI;
        const randomDistance: number = Math.random() * TELEPORT_RANGE + 2; // 2-12 blocks away

        const teleportLocation: Vector3 = {
          x: nearestPlayer.location.x + Math.cos(randomAngle) * randomDistance,
          y: nearestPlayer.location.y,
          z: nearestPlayer.location.z + Math.sin(randomAngle) * randomDistance
        };

        // Check if the calculated location is safe (air block)
        const targetBlock = werewolf.dimension.getBlock(teleportLocation);
        let finalTeleportLocation: Vector3;

        if (targetBlock && targetBlock.type.id === "minecraft:air") {
          // Target location is air, safe to teleport there
          finalTeleportLocation = teleportLocation;
        } else {
          // Target location is not air, fallback to player location
          finalTeleportLocation = nearestPlayer.location;
        }

        // Teleport werewolf to the calculated location
        werewolf.teleport(finalTeleportLocation);

        // Play teleport sound effect (using enderman portal sound)
        werewolf.dimension.playSound("mob.endermen.portal", finalTeleportLocation, {
          volume: 0.8,
          pitch: 1.2
        });
      }
    }
  } catch (error) {
    console.warn(`Failed to handle werewolf teleportation: ${error}`);
  }
}
