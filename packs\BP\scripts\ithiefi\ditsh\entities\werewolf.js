import { GameMode, EntityDamageCause, system } from "@minecraft/server";
import { getDistance } from "../utilities/vector3";
import { fixedLenRaycast } from "../utilities/raycasts";
const WEREWOLF_DRAIN_DAMAGE = 2;
const DETECTION_RADIUS = 128;
const HEALTH_DRAIN_INTERVAL = 20;
const TELEPORT_RANGE = 10;
const RAYCAST_RADIUS = 5;
const RAYCAST_STEP = 2;
function isPlayerLookingAtWerewolf(player, werewolfLocation) {
    try {
        const playerLocation = {
            x: player.location.x,
            y: player.location.y + 1.6,
            z: player.location.z
        };
        const viewDirection = player.getViewDirection();
        const maxDistance = getDistance(playerLocation, werewolfLocation);
        const raycastStep = RAYCAST_STEP;
        const detectionRadius = RAYCAST_RADIUS;
        const raycastPoints = fixedLenRaycast(playerLocation, viewDirection, maxDistance, raycastStep);
        for (const rayPoint of raycastPoints) {
            const distanceToWerewolf = getDistance(rayPoint, werewolfLocation);
            if (distanceToWerewolf <= detectionRadius) {
                if (rayPoint.y >= werewolfLocation.y && rayPoint.y <= werewolfLocation.y + 3.0) {
                    return true;
                }
            }
        }
        return false;
    }
    catch (error) {
        return false;
    }
}
const activeWerewolfEntities = new Map();
export function werewolfStartHealthDrain(werewolf) {
    try {
        const werewolfId = werewolf.id;
        if (activeWerewolfEntities.has(werewolfId)) {
            return;
        }
        const intervalId = system.runInterval(() => {
            try {
                if (!werewolf.isValid) {
                    system.clearRun(intervalId);
                    activeWerewolfEntities.delete(werewolfId);
                    return;
                }
                const werewolfLocation = werewolf.location;
                const players = werewolf.dimension.getPlayers({
                    location: werewolfLocation,
                    maxDistance: DETECTION_RADIUS,
                    excludeGameModes: [GameMode.Creative, GameMode.Spectator]
                });
                for (const player of players) {
                    if (isPlayerLookingAtWerewolf(player, werewolfLocation)) {
                        player.applyDamage(WEREWOLF_DRAIN_DAMAGE, {
                            cause: EntityDamageCause.entityAttack,
                            damagingEntity: werewolf
                        });
                        werewolf.dimension.playSound("damage.hit", player.location, {
                            volume: 0.3,
                            pitch: 0.8
                        });
                    }
                }
            }
            catch (error) {
                console.warn(`Failed to drain health for werewolf ${werewolfId}: ${error}`);
                system.clearRun(intervalId);
                activeWerewolfEntities.delete(werewolfId);
            }
        }, HEALTH_DRAIN_INTERVAL);
        activeWerewolfEntities.set(werewolfId, intervalId);
    }
    catch (error) {
        console.warn(`Failed to start werewolf health drain: ${error}`);
    }
}
export function werewolfStopHealthDrain(werewolf) {
    try {
        const werewolfId = werewolf.id;
        const intervalId = activeWerewolfEntities.get(werewolfId);
        if (intervalId !== undefined) {
            system.clearRun(intervalId);
            activeWerewolfEntities.delete(werewolfId);
        }
    }
    catch (error) {
        console.warn(`Failed to stop werewolf health drain: ${error}`);
    }
}
export function werewolfTeleportToPlayer(werewolf) {
    try {
        const werewolfLocation = werewolf.location;
        const players = werewolf.dimension.getPlayers({
            location: werewolfLocation,
            maxDistance: DETECTION_RADIUS,
            excludeGameModes: [GameMode.Creative, GameMode.Spectator]
        });
        if (players.length === 0) {
            return;
        }
        let anyPlayerLooking = false;
        for (const player of players) {
            if (isPlayerLookingAtWerewolf(player, werewolfLocation)) {
                anyPlayerLooking = true;
                break;
            }
        }
        if (!anyPlayerLooking) {
            let nearestPlayer = null;
            let nearestDistance = Infinity;
            for (const player of players) {
                const distance = getDistance(werewolfLocation, player.location);
                if (distance < nearestDistance) {
                    nearestDistance = distance;
                    nearestPlayer = player;
                }
            }
            if (nearestPlayer) {
                const randomAngle = Math.random() * 2 * Math.PI;
                const randomDistance = Math.random() * TELEPORT_RANGE + 2;
                const teleportLocation = {
                    x: nearestPlayer.location.x + Math.cos(randomAngle) * randomDistance,
                    y: nearestPlayer.location.y,
                    z: nearestPlayer.location.z + Math.sin(randomAngle) * randomDistance
                };
                const targetBlock = werewolf.dimension.getBlock(teleportLocation);
                let finalTeleportLocation;
                if (targetBlock && targetBlock.type.id === "minecraft:air") {
                    finalTeleportLocation = teleportLocation;
                }
                else {
                    finalTeleportLocation = nearestPlayer.location;
                }
                werewolf.teleport(finalTeleportLocation);
                werewolf.dimension.playSound("mob.endermen.portal", finalTeleportLocation, {
                    volume: 0.8,
                    pitch: 1.2
                });
            }
        }
    }
    catch (error) {
        console.warn(`Failed to handle werewolf teleportation: ${error}`);
    }
}
