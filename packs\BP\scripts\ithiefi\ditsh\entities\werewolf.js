import { GameMode, EntityDamageCause, system, world } from "@minecraft/server";
import { getDistance } from "../utilities/vector3";
import { fixedLenRaycast } from "../utilities/raycasts";
const BASE_WEREWOLF_DAMAGE = 2;
const DETECTION_RADIUS = 128;
const DAMAGE_START_TICKS = 60;
const DAMAGE_INCREMENT_TICKS = 40;
const playerLookAtTracker = new Map();
const RAYCAST_RADIUS = 5;
const RAYCAST_STEP = 2;
function isPlayerLookingAtWerewolf(player, werewolfLocation) {
    try {
        const playerLocation = {
            x: player.location.x,
            y: player.location.y + 1.6,
            z: player.location.z
        };
        const viewDirection = player.getViewDirection();
        const maxDistance = getDistance(playerLocation, werewolfLocation);
        const raycastStep = RAYCAST_STEP;
        const detectionRadius = RAYCAST_RADIUS;
        const raycastPoints = fixedLenRaycast(playerLocation, viewDirection, maxDistance, raycastStep);
        for (const rayPoint of raycastPoints) {
            const distanceToWerewolf = getDistance(rayPoint, werewolfLocation);
            if (distanceToWerewolf <= detectionRadius) {
                if (rayPoint.y >= werewolfLocation.y && rayPoint.y <= werewolfLocation.y + 3.0) {
                    return true;
                }
            }
        }
        return false;
    }
    catch (error) {
        return false;
    }
}
export function werewolfHealthDrain(werewolf) {
    try {
        const werewolfLocation = werewolf.location;
        const currentTick = system.currentTick;
        const players = werewolf.dimension.getPlayers({
            location: werewolfLocation,
            maxDistance: DETECTION_RADIUS,
            excludeGameModes: [GameMode.Creative, GameMode.Spectator]
        });
        const playersCurrentlyLooking = new Set();
        for (const player of players) {
            const playerId = player.id;
            if (isPlayerLookingAtWerewolf(player, werewolfLocation)) {
                playersCurrentlyLooking.add(playerId);
                if (!playerLookAtTracker.has(playerId)) {
                    playerLookAtTracker.set(playerId, currentTick);
                }
                const lookStartTick = playerLookAtTracker.get(playerId);
                const lookDuration = currentTick - lookStartTick;
                if (lookDuration >= DAMAGE_START_TICKS) {
                    const damageIntervals = Math.floor((lookDuration - DAMAGE_START_TICKS) / DAMAGE_INCREMENT_TICKS);
                    const damage = BASE_WEREWOLF_DAMAGE + damageIntervals;
                    player.applyDamage(damage, {
                        cause: EntityDamageCause.entityAttack,
                        damagingEntity: werewolf
                    });
                    werewolf.dimension.playSound("mob.ditsh.werewolf.drain", player.location);
                }
            }
        }
        for (const [playerId] of playerLookAtTracker) {
            if (!playersCurrentlyLooking.has(playerId)) {
                playerLookAtTracker.delete(playerId);
            }
        }
    }
    catch (error) {
        console.warn(`Failed to handle werewolf health drain: ${error}`);
    }
}
function getPlayersNotLookingAtWerewolf(werewolf) {
    const werewolfLocation = werewolf.location;
    const playersNotLooking = [];
    const players = werewolf.dimension.getPlayers({
        location: werewolfLocation,
        maxDistance: DETECTION_RADIUS,
        excludeGameModes: [GameMode.Creative, GameMode.Spectator]
    });
    for (const player of players) {
        if (!isPlayerLookingAtWerewolf(player, werewolfLocation)) {
            playersNotLooking.push(player);
        }
    }
    return playersNotLooking;
}
export function werewolfTeleportToPlayer(werewolf) {
    try {
        const playersNotLooking = getPlayersNotLookingAtWerewolf(werewolf);
        if (playersNotLooking.length === 0) {
            return;
        }
        const targetPlayer = playersNotLooking[Math.floor(Math.random() * playersNotLooking.length)];
        werewolf.teleport(targetPlayer.location);
        werewolf.dimension.playSound("mob.ditsh.werewolf.teleport", targetPlayer.location);
        playerLookAtTracker.delete(targetPlayer.id);
    }
    catch (error) {
        console.warn(`Failed to teleport werewolf to player: ${error}`);
    }
}
export function cleanupWerewolfPlayerTracking() {
    try {
        const connectedPlayerIds = new Set();
        for (const dimension of [world.getDimension("overworld"), world.getDimension("nether"), world.getDimension("the_end")]) {
            try {
                const players = dimension.getPlayers();
                for (const player of players) {
                    connectedPlayerIds.add(player.id);
                }
            }
            catch (error) {
                continue;
            }
        }
        for (const [playerId] of playerLookAtTracker) {
            if (!connectedPlayerIds.has(playerId)) {
                playerLookAtTracker.delete(playerId);
            }
        }
    }
    catch (error) {
        console.warn(`Failed to cleanup werewolf player tracking: ${error}`);
    }
}
