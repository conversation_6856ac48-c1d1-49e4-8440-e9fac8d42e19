import { Player, world, system } from "@minecraft/server";
import { setEntityToCardinalDirection } from "../utilities/rotation";
import { entitiesWithMusic, continueMusicForEntity, stopMusicForEntity, playMusicForEntity, resetPlayerMusic, cleanupExpiredMusic } from "./entitiesWithMusic";
import { mamaTattletailTeleportHandler } from "./mamaTattletail";
import { mxCheckForWallHit, mxJump } from "./mx";
import { scp096OnKillHandler } from "./scp096";
import { slendermanOnPlayerStartLooking, slendermanTeleportHandler } from "./slenderman";
import { specimen6TeleportBehindPlayer, specimen6TeleportToIdlePlayer, specimen6CheckIdlePlayers, cleanupSpecimen6PlayerTracking } from "./specimen6";
import { stalkerTeleportHandler } from "./stalker";
import { cleanupAllItemTrackers } from "../items/index";
import { vitaMimicOnPlayerStartLooking } from "./vitaMimic";
import { werewolfHealthDrain, werewolfTeleportToPlayer, cleanupWerewolfPlayerTracking } from "./werewolf";
export function initEntityListeners() {
    world.afterEvents.entitySpawn.subscribe((data) => {
        const entity = data.entity;
        const typeId = entity.typeId;
        if (typeId === "ditsh:door1" || typeId === "ditsh:door2") {
            setEntityToCardinalDirection(entity);
        }
    });
    world.afterEvents.entityLoad.subscribe((data) => {
        const entity = data.entity;
        const typeId = entity.typeId;
        if (entitiesWithMusic.has(typeId)) {
            continueMusicForEntity(entity, entitiesWithMusic.get(typeId));
        }
        else if (entity instanceof Player) {
            resetPlayerMusic(entity);
        }
    });
    world.afterEvents.dataDrivenEntityTrigger.subscribe(async (data) => {
        const entity = data.entity;
        const typeId = entity.typeId;
        const eventId = data.eventId;
        switch (eventId) {
            case "ditsh:start_chase_music":
                playMusicForEntity(entity, entitiesWithMusic.get(typeId));
                break;
            case "ditsh:maintain_chase_music":
                playMusicForEntity(entity, entitiesWithMusic.get(typeId));
                break;
            case "ditsh:stop_chase_music":
                stopMusicForEntity(entity, entitiesWithMusic.get(typeId));
                break;
            case "ditsh:on_death":
                stopMusicForEntity(entity, entitiesWithMusic.get(typeId));
                break;
            case "ditsh:teleport":
                mamaTattletailTeleportHandler(entity);
                break;
            case "ditsh:mx_check_for_wall_hit":
                mxCheckForWallHit(entity);
                break;
            case "ditsh:mx_jump":
                mxJump(entity);
                break;
            case "ditsh:on_kill":
                if (typeId === "ditsh:scp096") {
                    scp096OnKillHandler(entity);
                }
                break;
            case "ditsh:on_calm":
                if (typeId === "ditsh:scp096") {
                    scp096OnKillHandler(entity);
                }
                break;
            case "ditsh:on_player_start_looking":
                if (typeId === "ditsh:slenderman") {
                    slendermanOnPlayerStartLooking(entity);
                }
                else if (typeId === "ditsh:vita_mimic") {
                    vitaMimicOnPlayerStartLooking(entity);
                }
                break;
            case "ditsh:teleport_to_player":
                if (typeId === "ditsh:slenderman") {
                    slendermanTeleportHandler(entity);
                }
                else if (typeId === "ditsh:stalker") {
                    stalkerTeleportHandler(entity);
                }
                break;
            case "ditsh:teleport_behind_player":
                specimen6TeleportBehindPlayer(entity);
                break;
            case "ditsh:teleport_to_idle_player":
                specimen6TeleportToIdlePlayer(entity);
                break;
            case "ditsh:check_idle_players":
                specimen6CheckIdlePlayers(entity);
                break;
            case "ditsh:start_fog_interval":
                break;
            case "ditsh:check_for_players":
                break;
            case "ditsh:teleport_and_kill":
                if (entity.typeId === "ditsh:werewolf") {
                    werewolfHealthDrain(entity);
                }
                break;
            case "ditsh:teleport_to_player":
                if (entity.typeId === "ditsh:werewolf") {
                    werewolfTeleportToPlayer(entity);
                }
                break;
            default:
                break;
        }
    });
    system.runInterval(() => {
        cleanupExpiredMusic();
        cleanupSpecimen6PlayerTracking();
        cleanupWerewolfPlayerTracking();
        cleanupAllItemTrackers();
    }, 200);
}
