{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "ditsh:werewolf", "is_spawnable": true, "is_summonable": true}, "component_groups": {"ditsh:active": {"minecraft:looked_at": {"search_radius": 128.0, "look_at_locations": [{"location": "head"}, {"location": "body"}, {"location": "feet", "vertical_offset": 0.4}], "set_target": "never", "find_players_only": true, "looked_at_cooldown": [2, 4], "field_of_view": 5, "scale_fov_by_distance": false, "line_of_sight_obstruction_type": "collision_for_camera", "looked_at_event": {"event": "ditsh:teleport_and_kill", "target": "self"}, "filters": {"all_of": [{"test": "actor_health", "subject": "other", "operator": ">", "value": 0}, {"test": "is_family", "subject": "other", "value": "player"}]}}}}, "components": {"minecraft:type_family": {"family": ["monster", "werewolf", "ditsh", "mob"]}, "minecraft:collision_box": {"width": 0.8, "height": 2.8}, "minecraft:health": {"value": 80, "max": 80}, "minecraft:attack": {"damage": 20}, "minecraft:movement": {"value": 0.0}, "minecraft:navigation.walk": {"can_path_over_water": false, "avoid_water": true, "avoid_damage_blocks": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:behavior.float": {"priority": 0}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": false}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:persistent": {}, "minecraft:knockback_resistance": {"value": 1, "max": 1}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["ditsh:active"]}}, "ditsh:teleport_and_kill": {"trigger": {"event": "ditsh:teleport_and_kill", "target": "self"}}, "ditsh:teleport_to_player": {"trigger": {"event": "ditsh:teleport_to_player", "target": "self"}}, "ditsh:on_death": {"trigger": {"event": "ditsh:on_death", "target": "self"}}, "minecraft:entity_born": {"add": {"component_groups": ["ditsh:active"]}}}}}